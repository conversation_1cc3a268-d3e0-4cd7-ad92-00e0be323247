import React, {useState, useRef, useEffect} from 'react';
import {ChevronDown, ChevronUp} from 'react-bootstrap-icons';
import classNames from 'classnames';
import '../styles/components/vessel-and-office-dropdown.scss';


type Option = {
  id: number;
  label: string;
};

interface MultiSelectCheckboxDropdownProps {
  options: Option[];
  selected: number[];
  onChange: (selected: number[]) => void;
  placeholder?: string;
}

const MultiSelectCheckboxDropdown: React.FC<MultiSelectCheckboxDropdownProps> =
  ({options, selected, onChange, placeholder = 'Select options'}) => {
    const [open, setOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const toggleOption = (id: number) => {
      if (selected.includes(id)) {
        onChange(selected.filter(val => val !== id));
      } else {
        onChange([...selected, id]);
      }
    };

    useEffect(() => {
      const handleClickOutside = (e: MouseEvent) => {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(e.target as Node)
        ) {
          setOpen(false);
        }
      };
      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const selectedLabels = options
      .filter(opt => selected.includes(opt.id))
      .map(opt => opt.label)
      .join(', ');

    return (
      <div className="simple-multiselect-dropdown" ref={dropdownRef}>
        <div
          className={classNames('dropdown-toggle', {open})}
          onClick={() => setOpen(!open)}
          role="button"
          tabIndex={0}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              setOpen(!open);
              e.preventDefault();
            }
          }}
        >
          <span className="dropdown-label">
            {selected.length > 0 ? selectedLabels : placeholder}
          </span>
          {open ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
        </div>

        {open && (
          <div className="dropdown-menu">
            {options.map(opt => (
              <div
                key={opt.id}
                className="dropdown-item"
                onClick={() => toggleOption(opt.id)}
                role="checkbox"
                aria-checked={selected.includes(opt.id)}
                tabIndex={0}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    toggleOption(opt.id);
                    e.preventDefault();
                  }
                }}
              >
                <input
                  type="checkbox"
                  checked={selected.includes(opt.id)}
                  readOnly
                />
                <span>{opt.label}</span>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

export default MultiSelectCheckboxDropdown;
