import {TemplateFormStatus} from '../enums';
import {
  Parameter,
  TemplateForm,
  TemplateFormCategory,
  TemplateFormHazard,
  TemplateFormJobResidualRiskRating,
} from '../types/template';
import {v4 as uuidv4} from 'uuid';
import * as _ from 'lodash';
import {riskMatrix} from '../constants/riskMatrix';
import {
  RiskForm,
  RiskCategory,
  RiskHazard,
  RiskFormJob,
  RiskTaskReliabilityAssessment,
  RiskJobResidualRiskRating,
  RiskJobInitialRiskRating,
  RiskTeamMember,
} from '../types/risk';

type RiskParameterTypeInput = {
  id: number;
  name: string;
  parameters: {id: number; name: string}[];
};

type GroupedOption = {
  id: number;
  label: string;
  options: {id: number; label: string}[];
  columns: number;
};

export function generateGroupedOptions(
  riskParameterType: RiskParameterTypeInput[],
  columns = 3,
): GroupedOption[] {
  return riskParameterType.map(group => ({
    id: group.id,
    label: group.name.toUpperCase(),
    options: group.parameters.map(opt => ({
      id: opt.id,
      label: opt.name,
    })),
    columns,
  }));
}

// Utility to remove and reindex job state objects after a job is deleted
export function removeAndReindexJobState<T extends Record<number, any>>(
  state: T,
  idx: number,
): T {
  const newState: T = {} as T;
  Object.keys(state)
    .map(Number)
    .filter(i => i !== idx)
    .sort((a, b) => a - b)
    .forEach(i => {
      newState[i < idx ? i : i - 1] = state[i];
    });
  return newState;
}

export const formParameterHandler = (payload: any) => {
  if (!payload?.template_category?.category_id?.length) {
    delete payload.template_category;
  }
  if (!payload?.risk_category?.category_id?.length) {
    delete payload.risk_category;
  }
  if (
    !payload?.template_hazard?.hazard_id?.length &&
    !payload?.template_hazard?.is_other
  ) {
    delete payload.template_hazard;
  }
  if (!payload?.template_job?.[0]?.job_step?.length) {
    delete payload.template_job;
  }
  if (
    !payload?.risk_category?.[0]?.isOther &&
    payload?.risk_category?.value === ''
  ) {
    delete payload.risk_category.value;
  }
  if (
    !payload?.template_category?.[0]?.isOther &&
    payload?.template_category?.value === ''
  ) {
    delete payload.template_category.value;
  }
  if (
    !payload?.risk_hazard?.[0]?.isOther &&
    payload?.risk_hazard?.value === ''
  ) {
    delete payload.risk_hazard.value;
  }
  if (
    !payload?.template_hazard?.[0]?.isOther &&
    payload?.template_hazard?.value === ''
  ) {
    delete payload.template_hazard.value;
  }

  // Handle risk_hazard deletion if hazard_id is empty and is_other is false
  if (
    !payload?.risk_hazard?.hazard_id?.length &&
    !payload?.risk_hazard?.is_other
  ) {
    delete payload.risk_hazard;
  }

  // Handle empty risk form fields for save as draft
  if (!payload?.worst_case_scenario?.length) {
    delete payload.worst_case_scenario;
  }
  if (!payload?.recovery_measures?.length) {
    delete payload.recovery_measures;
  }
  payload.parameters = (payload?.parameters as Parameter[] | undefined)?.filter(
    (param: Parameter) =>
      (param?.parameter_id && param.parameter_id.length) || param?.is_other,
  );

  if (Array.isArray(payload.template_job)) {
    payload.template_job = payload.template_job.map(
      ({
        id,
        job_id,
        template_job_residual_risk_rating,
        template_job_initial_risk_rating,
        job_close_out_date,
        job_close_out_responsibility_id,
        ...rest
      }: {
        job_id?: any;
        [key: string]: any;
      }) => ({
        ...rest,
        ...(Array.isArray(template_job_residual_risk_rating) && {
          template_job_residual_risk_rating:
            template_job_residual_risk_rating.map(
              ({rating, parameter_type_id, reason}: any) => {
                const base = {rating, parameter_type_id};
                return reason ? {...base, reason} : base;
              },
            ),
        }),
        ...(Array.isArray(template_job_initial_risk_rating) && {
          template_job_initial_risk_rating:
            template_job_initial_risk_rating.map(
              ({rating, parameter_type_id}: any) => ({
                rating,
                parameter_type_id,
              }),
            ),
        }),
      }),
    );
  }
  if (payload.template_task_reliability_assessment?.length) {
    payload.template_task_reliability_assessment =
      payload.template_task_reliability_assessment.map((item: any) => {
        const {condition} = item;
        const newItem = {
          task_reliability_assessment_answer:
            item?.task_reliability_assessment_answer ||
            item?.task_reliability_assessmen ||
            '',
          task_reliability_assessment_id:
            item?.task_reliability_assessment_id || item?.id || null,
        };
        return !condition?.length ? newItem : {...newItem, condition};
      });
  }
  if (payload.risk_task_reliability_assessment?.length) {
    payload.risk_task_reliability_assessment =
      payload.risk_task_reliability_assessment.map((item: any) => {
        const {condition} = item;
        const newItem = {
          task_reliability_assessment_answer:
            item?.task_reliability_assessment_answer ||
            item?.task_reliability_assessmen ||
            '',
          task_reliability_assessment_id:
            item?.task_reliability_assessment_id || item?.id || null,
        };
        return !condition?.length
          ? newItem
          : {...newItem, condition, task_reliability_assessment_answer: 'Yes'};
      });
  }
  // Handle RiskForm hazard
  if (payload?.risk_hazard) {
    if (!payload?.risk_hazard?.is_other && payload?.risk_hazard?.value === '') {
      delete payload.risk_hazard.value;
    }
  }

  // Handle risk jobs
  if (Array.isArray(payload.risk_job)) {
    payload.risk_job = payload.risk_job.map(
      ({
        risk_job_residual_risk_rating,
        risk_job_initial_risk_rating,
        job_close_out_date,
        job_close_out_responsibility_id,
        ...rest
      }: {
        [key: string]: any;
      }) => {
        const processedJob: any = {...rest};

        // Handle risk ratings
        if (Array.isArray(risk_job_residual_risk_rating)) {
          processedJob.risk_job_residual_risk_rating = risk_job_residual_risk_rating.map(
            ({rating, parameter_type_id, reason}: any) => {
              const base = {rating, parameter_type_id};
              return reason ? {...base, reason} : base;
            },
          );
        }

        if (Array.isArray(risk_job_initial_risk_rating)) {
          processedJob.risk_job_initial_risk_rating = risk_job_initial_risk_rating.map(
            ({rating, parameter_type_id}: any) => ({
              rating,
              parameter_type_id,
            }),
          );
        }

        // Only include job_close_out_date if it's not empty
        if (job_close_out_date && job_close_out_date.length > 0) {
          processedJob.job_close_out_date = job_close_out_date;
        }

        // Only include job_close_out_responsibility_id if it's not empty
        if (job_close_out_responsibility_id && job_close_out_responsibility_id.length > 0) {
          processedJob.job_close_out_responsibility_id = job_close_out_responsibility_id;
        }

        return processedJob;
      }
    );

    // Filter out risk jobs that have empty required fields for save as draft
    payload.risk_job = payload.risk_job.filter((job: any) => {
      // Keep job if at least job_step has content (minimum requirement)
      return job.job_step && job.job_step.length > 0;
    });

    // If no valid risk jobs remain, delete the entire risk_job array
    if (payload.risk_job.length === 0) {
      delete payload.risk_job;
    }
  }

  // Handle risk task reliability assessment
  if (payload.risk_task_reliability_assessment?.length) {
    payload.risk_task_reliability_assessment =
      payload.risk_task_reliability_assessment.map((item: any) => {
        const {condition} = item;
        const newItem = {
          task_reliability_assessment_answer:
            item?.task_reliability_assessment_answer ||
            item?.task_reliability_assessmen ||
            '',
          task_reliability_assessment_id:
            item?.task_reliability_assessment_id || item?.id || null,
        };
        return !condition?.length ? newItem : {...newItem, condition};
      });
  }

  // Remove 'value' key if 'is_other' is false in each parameter
  if (Array.isArray(payload.parameters)) {
    payload.parameters = payload.parameters.map((param: Parameter) => {
      if (param?.parameter_id?.length) {
        param.parameter_id = _.uniq(param.parameter_id);
      }
      if (param && param.is_other === false) {
        const {value, ...rest} = param;
        return rest;
      }

      return param;
    });
  }
  if (!payload?.approval_required?.length) {
    delete payload.approval_required;
  }
  if (payload?.id) {
    delete payload.id;
  }
  if (!payload?.task_duration?.length) {
    delete payload.task_duration;
  }

  if (payload?.risk_team_member?.length) {
    payload.risk_team_member = payload.risk_team_member.map(
      ({id, risk_id, ...rest}: any) => rest,
    );
  }
  if (!payload?.assessor) {
    delete payload.assessor;
  }
  if (!payload?.date_risk_assessment) {
    delete payload.date_risk_assessment;
  }
  if (!payload?.vessel_ownership_id) {
    delete payload.vessel_ownership_id;
  }
  if (payload?.updated_at) {
    delete payload.updated_at;
  }
  if (payload?.created_by) {
    delete payload.created_by;
  }
  if (payload?.updated_by) {
    delete payload.updated_by;
  }
  return payload;
};
// Helper to map incoming parameters to required Parameter[] format
function mapParameters(input: any[]): Parameter[] {
  if (!Array.isArray(input)) return [];
  return _.chain(input)
    .groupBy(item => item?.parameterType?.id) // TBC parameter_type_id
    .map(items => {
      const hasOtherValue =
        items.filter(item => item.parameter_is_other)?.[0]?.value ?? '';
      return {
        is_other: !!hasOtherValue,
        parameter_type_id: items[0]?.parameterType?.id, // TBC parameter_type_id
        parameter_id: _.compact(_.map(items, 'parameter.id')) ?? [],
        value: hasOtherValue,
      };
    })
    .value();
}
function mapHazards(input: any[]): TemplateFormHazard {
  const hasOtherValue =
    input?.filter(item => item.hazard_category_is_other)?.[0]?.value ?? '';
  return {
    is_other: !!hasOtherValue,
    value: hasOtherValue,
    hazard_id:
      _.compact(
        input
          ?.filter(
            item => !item?.hazard_category_is_other && item?.hazard_detail,
          )
          .map(item => item?.hazard_detail?.id),
      ) ?? [],
  };
}

function mapCategories(input: any[]): TemplateFormCategory {
  const hasOtherValue =
    input?.filter(item => item.category_is_other)?.[0]?.value ?? '';
  return {
    is_other: !!hasOtherValue,
    value: hasOtherValue,
    category_id:
      _.compact(
        input
          ?.filter(item => !item?.category_is_other && item?.category_detail)
          .map(item => item?.category_detail?.id),
      ) ?? [],
  };
}

export const createFormFromData = (data?: any): TemplateForm => {
  return {
    task_requiring_ra: data?.task_requiring_ra ?? '',
    task_duration: data?.task_duration ?? '',
    task_alternative_consideration: data?.task_alternative_consideration ?? '',
    task_rejection_reason: data?.task_rejection_reason ?? '',
    worst_case_scenario: data?.worst_case_scenario ?? '',
    recovery_measures: data?.recovery_measures ?? '',
    status: TemplateFormStatus.DRAFT,

    parameters: data?.template_parameter?.length
      ? mapParameters(data?.template_parameter)
      : [],

    template_category: mapCategories(data?.template_category),
    template_job: data?.template_job?.length
      ? data?.template_job?.map((job: any) => ({
          ...job,
          job_id: uuidv4(),
        }))
      : [
          {
            job_id: uuidv4(),
            job_step: '',
            job_hazard: '',
            job_nature_of_risk: '',
            job_existing_control: '',
            job_additional_mitigation: '',
            job_close_out_date: '2025-05-21',
            job_close_out_responsibility_id: '1',
            template_job_initial_risk_rating: [],
            template_job_residual_risk_rating: [],
          },
        ],
    template_hazard: mapHazards(data?.template_hazards),
    template_task_reliability_assessment:
      data?.template_task_reliability_assessment ?? [],
    template_keyword: data?.template_keyword ?? ['test'],
    updated_at: data?.updated_at ?? undefined,
    updated_by: data?.updated_by ?? undefined,
    created_by: data?.created_by ?? undefined,
  };
};

export const validateRequiredField = (value?: string): boolean => {
  return !value?.trim();
};

export const validateTaskReliabilityAnswers = (assessments: any[]): boolean => {
  return (
    !assessments.length ||
    assessments.some((item: any) => !item.task_reliability_assessment_answer)
  );
};

export const getAssessmentArray = (form: TemplateForm | RiskForm): any[] => {
  if ('template_task_reliability_assessment' in form) {
    return Array.isArray(form.template_task_reliability_assessment)
      ? form.template_task_reliability_assessment
      : [];
  } else {
    return Array.isArray(form.risk_task_reliability_assessment)
      ? form.risk_task_reliability_assessment
      : [];
  }
};

// Calculate risk rating based on answers and riskMatrix
export const calculateRiskRating = (form: TemplateForm | RiskForm): string => {
  // Support both TemplateForm and RiskForm for assessment array
  let assessments: any[] = [];

  if ('template_task_reliability_assessment' in form) {
    if (Array.isArray(form.template_task_reliability_assessment)) {
      assessments = form.template_task_reliability_assessment;
    }
  } else if (
    Array.isArray((form as RiskForm).risk_task_reliability_assessment)
  ) {
    assessments = (form as RiskForm).risk_task_reliability_assessment;
  }

  const answers = assessments.map(
    (item: any) => item.task_reliability_assessment_answer,
  );

  // Existing logic: if any answer is 'No', return 'High'
  const anyNo = answers.some(ans => ans === 'No');
  if (anyNo) return 'High';

  // New logic: calculate max rate from all jobs' RRRs
  let maxRate = 0;

  // Handle jobs for both TemplateForm and RiskForm
  let jobs: any[] = [];

  if ('template_job' in form) {
    if (Array.isArray(form.template_job)) {
      jobs = form.template_job;
    }
  } else if (Array.isArray((form as RiskForm).risk_job)) {
    jobs = (form as RiskForm).risk_job;
  }

  jobs.forEach(job => {
    // For TemplateForm
    if ('template_job_residual_risk_rating' in job) {
      if (Array.isArray(job.template_job_residual_risk_rating)) {
        job.template_job_residual_risk_rating.forEach(
          (rrr: TemplateFormJobResidualRiskRating) => {
            if (rrr.rating) {
              const matrixItem = riskMatrix.find(
                item => item.value === rrr.rating,
              );
              if (matrixItem && typeof matrixItem.rate === 'number') {
                if (matrixItem.rate > maxRate) {
                  maxRate = matrixItem.rate;
                }
              }
            }
          },
        );
      }
    }
    // For RiskForm
    if ('risk_job_residual_risk_rating' in job) {
      if (Array.isArray(job.risk_job_residual_risk_rating)) {
        job.risk_job_residual_risk_rating.forEach((rrr: any) => {
          if (rrr.rating) {
            const matrixItem = riskMatrix.find(
              item => item.value === rrr.rating,
            );
            if (matrixItem && typeof matrixItem.rate === 'number') {
              if (matrixItem.rate > maxRate) {
                maxRate = matrixItem.rate;
              }
            }
          }
        });
      }
    }
  });

  // Find the class for the maxRate
  if (maxRate > 0) {
    const maxRateObj = riskMatrix.find(item => item.rate === maxRate);
    if (maxRateObj) {
      return (
        maxRateObj.class.charAt(0).toUpperCase() +
        maxRateObj.class.slice(1).toLowerCase()
      );
    }
  }

  // Default fallback
  return 'Medium';
};

// Helper functions for risk rating colors
export const getRiskRatingBackgroundColor = (rating: string): string => {
  switch (rating) {
    case 'High':
      return '#FAF2F5';
    case 'Medium':
      return '#FFF8E1';
    default:
      // Low
      return '#F2FAF2';
  }
};

export const getRiskRatingTextColor = (rating: string): string => {
  switch (rating) {
    case 'High':
      return '#C82333';
    case 'Medium':
      return '#FFA500';
    default:
      // Low
      return '#218838';
  }
};

// Helper functions for RiskForm mapping
function mapRiskCategory(input?: any): RiskCategory {
  return {
    is_other: input?.is_other ?? false,
    category_id: _.compact(input?.map((item: any) => item.category.id)) ?? [],
    value: input?.value ?? '',
  };
}

function mapRiskJobs(input?: any[]): RiskFormJob[] {
  if (!Array.isArray(input) || !input.length) {
    return [
      {
        job_step: '',
        job_hazard: '',
        job_nature_of_risk: '',
        job_additional_mitigation: '',
        job_close_out_date: '',
        job_existing_control: '',
        job_close_out_responsibility_id: '',
        risk_job_initial_risk_rating: [],
        risk_job_residual_risk_rating: [],
      },
    ];
  }

  return input.map(job => ({
    job_step: job?.job_step ?? '',
    job_hazard: job?.job_hazard ?? '',
    job_nature_of_risk: job?.job_nature_of_risk ?? '',
    job_additional_mitigation: job?.job_additional_mitigation ?? '',
    job_close_out_date: job?.job_close_out_date ?? '',
    job_existing_control: job?.job_existing_control ?? '',
    job_close_out_responsibility_id: job?.job_close_out_responsibility_id ?? '',
    risk_job_initial_risk_rating: job?.risk_job_initial_risk_rating ?? [],
    risk_job_residual_risk_rating: job?.risk_job_residual_risk_rating ?? [],
  }));
}

function mapRiskTaskReliabilityAssessment(
  input?: any[],
): RiskTaskReliabilityAssessment[] {
  if (!Array.isArray(input)) return [];
  return input.map(assessment => ({
    task_reliability_assessment_id:
      assessment?.task_reliability_assessment_id ?? 0,
    task_reliability_assessment_answer:
      assessment?.task_reliability_assessment_answer ?? '',
    condition: assessment?.condition ?? '',
  }));
}

export const createRiskFormFromData = (data?: any): RiskForm => {
  return {
    template_id: data?.template_id ?? undefined,
    task_requiring_ra: data?.task_requiring_ra ?? '',
    assessor: data?.assessor ?? undefined,
    vessel_ownership_id: data?.vessel_ownership_id ?? 0,
    vessel_id: data?.vessel_id ?? undefined,
    date_risk_assessment: data?.date_risk_assessment ?? undefined,
    task_duration: data?.task_duration ?? '',
    task_alternative_consideration: data?.task_alternative_consideration ?? '',
    task_rejection_reason: data?.task_rejection_reason ?? '',
    worst_case_scenario: data?.worst_case_scenario ?? '',
    recovery_measures: data?.recovery_measures ?? '',
    status: 'DRAFT',
    approval_required: data?.risk_approval_required?.length
      ? data?.risk_approval_required?.map(
          (item: any) => item?.approval_required?.id,
        )
      : [],
    risk_team_member: data?.risk_team_member ?? [],
    risk_category: mapRiskCategory(data?.risk_category),
    risk_hazard: mapHazards(data?.risk_hazards),
    parameters: mapParameters(data?.risk_parameter),
    risk_job: mapRiskJobs(data?.risk_job),
    risk_task_reliability_assessment: mapRiskTaskReliabilityAssessment(
      data?.risk_task_reliability_assessment,
    ),
    updated_at: data?.updated_at ?? undefined,
    updated_by: data?.updated_by ?? undefined,
    created_by: data?.created_by ?? undefined,
  };
};

// Converts template API response to RAForm object using RiskItem for types
export const transformTemplateToRisk = (payload: any): RiskForm => {
  // Map team members (if any, else empty array)
  const risk_team_member: RiskTeamMember[] = [];

  // Map risk category
  const risk_category: RiskCategory = {
    is_other: false,
    category_id: Array.isArray(payload?.template_category)
      ? payload.template_category.map((item: any) => item?.category?.id)
      : [],
    value: '', // No value in template_category, set as empty string
  };

  // Map risk hazard
  const risk_hazard: RiskHazard = {
    is_other: !!payload?.template_hazards?.find(
      (h: any) => h.hazard_category_is_other,
    ),
    hazard_id: Array.isArray(payload?.template_hazards)
      ? payload.template_hazards
          .filter((h: any) => !h.hazard_category_is_other && h.hazard_detail)
          .map((h: any) => h.hazard_detail?.id)
      : [],
    value:
      payload?.template_hazards?.find((h: any) => h.hazard_category_is_other)
        ?.value || '',
  };

  // Map parameters
  const parameters: Parameter[] = Array.isArray(payload?.template_parameter)
    ? (() => {
        // Group by parameterType.id
        const grouped: {[typeId: number]: any[]} = {};
        payload.template_parameter.forEach((item: any) => {
          const typeId = item?.parameterType?.id;
          if (!typeId) return;
          if (!grouped[typeId]) grouped[typeId] = [];
          grouped[typeId].push(item);
        });
        return Object.values(grouped).map((items: any[]) => {
          const is_other = !!items.find(i => i.parameter_is_other);
          const value = items.find(i => i.parameter_is_other)?.value ?? '';
          return {
            is_other,
            parameter_type_id: items[0]?.parameterType?.id,
            parameter_id: _.uniq(
              items.filter(i => i?.parameter?.id).map(i => i.parameter.id),
            ),
            value: is_other ? value : '',
          };
        });
      })()
    : [];

  // Map jobs
  const risk_job: RiskFormJob[] = Array.isArray(payload?.template_job)
    ? payload.template_job.map((job: any) => ({
        job_step: job.job_step ?? '',
        job_hazard: job.job_hazard ?? '',
        job_nature_of_risk: job.job_nature_of_risk ?? '',
        job_additional_mitigation: job.job_additional_mitigation ?? '',
        job_close_out_date: job.job_close_out_date ?? undefined,
        job_existing_control: job.job_existing_control ?? '',
        job_close_out_responsibility_id:
          job.job_close_out_responsibility_id ?? '2291',
        risk_job_initial_risk_rating: Array.isArray(
          job.template_job_initial_risk_rating,
        )
          ? job.template_job_initial_risk_rating.map(
              (r: any): RiskJobInitialRiskRating => ({
                parameter_type_id: r.parameter_type_id ?? 0,
                rating: r.rating,
              }),
            )
          : [],
        risk_job_residual_risk_rating: Array.isArray(
          job.template_job_residual_risk_rating,
        )
          ? job.template_job_residual_risk_rating.map(
              (
                r: TemplateFormJobResidualRiskRating,
              ): RiskJobResidualRiskRating => ({
                parameter_type_id: r.parameter_type_id ?? 0,
                rating: r.rating,
                reason: r.reason ?? '',
              }),
            )
          : [],
      }))
    : [];

  // Map task reliability assessment
  const risk_task_reliability_assessment: RiskTaskReliabilityAssessment[] =
    Array.isArray(payload?.template_task_reliability_assessment)
      ? payload.template_task_reliability_assessment.map((item: any) => ({
          task_reliability_assessment_id:
            item.task_reliability_assessment_id ?? item.id ?? 0,
          task_reliability_assessment_answer:
            item.task_reliability_assessment_answer ?? '',
          condition: item.condition ?? '',
        }))
      : [];

  // Map approval_required (not present in template, so set as empty array)
  // Map vessel_ownership_id (from RiskItem if present, else 0)
  // Map assessor (from RiskItem if present, else 0)
  // Map date_risk_assessment (from RiskItem if present, else '')

  return {
    task_requiring_ra: payload?.task_requiring_ra ?? '',
    assessor: payload?.assessor ?? null,
    vessel_ownership_id: payload?.vessel_ownership_id ?? null,
    date_risk_assessment: payload?.date_risk_assessment ?? null,
    task_duration: payload?.task_duration ?? '',
    task_alternative_consideration:
      payload?.task_alternative_consideration ?? '',
    task_rejection_reason: payload?.task_rejection_reason ?? '',
    worst_case_scenario: payload?.worst_case_scenario ?? '',
    recovery_measures: payload?.recovery_measures ?? '',
    status: TemplateFormStatus.DRAFT,
    approval_required: [],
    risk_team_member,
    risk_category,
    risk_hazard,
    parameters,
    risk_job,
    risk_task_reliability_assessment,
    updated_at: payload?.updated_at ?? undefined,
    updated_by: payload?.updated_by ?? undefined,
    created_by: payload?.created_by ?? undefined,
  };
};

// Helper function to format date to YYYY-MM-DD without timezone issues
export const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};
