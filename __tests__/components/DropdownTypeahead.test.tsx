import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DropdownTypeahead, {
  Option,
} from '../../src/components/DropdownTypeahead';

describe('DropdownTypeahead', () => {
  const mockOptions: Option[] = [
    {value: '1', label: 'Option 1'},
    {value: '2', label: 'Option 2'},
    {value: '3', label: 'Option 3'},
  ];

  const defaultProps = {
    label: 'Test Dropdown',
    options: mockOptions,
    selected: null,
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with proper accessibility attributes', () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    expect(input).toHaveAttribute('aria-autocomplete', 'both');
  });

  it('hides label when hideLabel prop is true', () => {
    render(
      <DropdownTypeahead {...defaultProps} hideLabel={true} />,
    );
    const label = screen.getByText('Test Dropdown');
    expect(label).toHaveStyle({display: 'none'});
  });

  it('disables the input when disabled prop is true', () => {
    render(<DropdownTypeahead {...defaultProps} disabled={true} />);

    const input = screen.getByRole('combobox');
    expect(input).toBeDisabled();
  });

  it('shows error message when isInvalid is true', () => {
    const errorMessage = 'Custom error message';
    render(
      <DropdownTypeahead
        {...defaultProps}
        isInvalid={true}
        errorMessage={errorMessage}
      />,
    );

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('handles single selection correctly', async () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const option = await screen.findByText('Option 1');
    fireEvent.click(option);

    expect(defaultProps.onChange).toHaveBeenCalledWith({
      value: '1',
      label: 'Option 1',
    });
  });

  it('handles multiple selection correctly', async () => {
    const onChange = jest.fn();
    render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        onChange={onChange}
        selected={[]}
      />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const option1 = await screen.findByText('Option 1');
    fireEvent.click(option1);

    expect(onChange).toHaveBeenCalledWith([
      {
        value: '1',
        label: 'Option 1',
      },
    ]);
  });

  it('calls onInputChange when typing in the input', async () => {
    const onInputChange = jest.fn();
    render(
      <DropdownTypeahead {...defaultProps} onInputChange={onInputChange} />,
    );

    const input = screen.getByRole('combobox');
    await userEvent.type(input, 't');

    // Only check the first argument since the second is a React synthetic event
    expect(onInputChange.mock.calls[0][0]).toBe('t');
  });

  it('displays "No results found" when no options match search', async () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    await userEvent.type(input, 'nonexistent');

    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('handles special option selection', async () => {
    const onSpecialOptionSelect = jest.fn();
    render(
      <DropdownTypeahead
        {...defaultProps}
        specialOptionLabel="Add New Option"
        onSpecialOptionSelect={onSpecialOptionSelect}
      />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const specialOption = await screen.findByText('Add New Option');
    fireEvent.click(specialOption);

    expect(onSpecialOptionSelect).toHaveBeenCalled();
  });

  it('displays token with more count for multiple selection', () => {
    const selected = [
      {value: '1', label: 'Option 1'},
      {value: '2', label: 'Option 2'},
      {value: '3', label: 'Option 3'},
      {value: '4', label: 'Option 4'},
      {value: '5', label: 'Option 5'},
    ];

    const {container} = render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        selected={selected}
      />,
    );

    expect(
      screen.getByText('Option 1, Option 2, Option 3, Option 4'),
    ).toBeInTheDocument();

    const element = container.getElementsByClassName('rbt-token-more')[0];
    expect(element).toHaveTextContent('+1 More');
  });

  it('clears selection when clear button is clicked', async () => {
    const onChange = jest.fn();
    const selected = {value: '1', label: 'Option 1'};
    render(
      <DropdownTypeahead
        {...defaultProps}
        selected={selected}
        onChange={onChange}
      />,
    );

    const clearButton = screen
      .getByTestId('clear-icon')
      .querySelector('button');
    fireEvent.click(clearButton!);

    expect(onChange).toHaveBeenCalledWith(null);
  });

  it('toggles dropdown when clear icon is clicked with no selection', () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const clearButton = screen
      .getByTestId('clear-icon')
      .querySelector('button');
    fireEvent.click(clearButton!);

    expect(screen.getByTestId('clear-icon')).toBeInTheDocument();
  });

  it('renders custom placeholder when disabledSelectPrefix is true', () => {
    render(<DropdownTypeahead {...defaultProps} disabledSelectPrefix={true} />);

    const input = screen.getByRole('combobox');
    expect(input).toHaveAttribute('placeholder', 'Test Dropdown');
  });

  it('shows required indicator when required prop is true', () => {
    render(<DropdownTypeahead {...defaultProps} required={true} />);

    const labelElement = screen.getByText('Test Dropdown*');
    expect(labelElement).toBeInTheDocument();
  });

  it('handles non-object options correctly', async () => {
    const stringOptions = ['Option 1', 'Option 2', 'Option 3'];
    render(
      <DropdownTypeahead {...defaultProps} options={stringOptions as any[]} />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const option = await screen.findByText('Option 1');
    expect(option).toBeInTheDocument();
  });

  describe('Checkbox functionality', () => {
    const checkboxProps = {
      ...defaultProps,
      multiple: true,
      useCheckboxes: true,
    };

    it('renders checkboxes when useCheckboxes and multiple are true', async () => {
      render(<DropdownTypeahead {...checkboxProps} />);

      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByText('Option 1')).toBeInTheDocument();
      });

      // Check if checkboxes are rendered
      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes).toHaveLength(3);
    });

    it('handles checkbox selection correctly', async () => {
      const mockOnChange = jest.fn();
      render(<DropdownTypeahead {...checkboxProps} onChange={mockOnChange} />);

      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByText('Option 1')).toBeInTheDocument();
      });

      // Click on the first checkbox
      const firstCheckbox = screen.getAllByRole('checkbox')[0];
      fireEvent.click(firstCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith([{value: '1', label: 'Option 1'}]);
    });

    it('handles checkbox deselection correctly', async () => {
      const mockOnChange = jest.fn();
      render(
        <DropdownTypeahead
          {...checkboxProps}
          onChange={mockOnChange}
          selected={[{value: '1', label: 'Option 1'}]}
        />
      );

      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByText('Option 1')).toBeInTheDocument();
      });

      // Click on the first checkbox to deselect
      const firstCheckbox = screen.getAllByRole('checkbox')[0];
      expect(firstCheckbox).toBeChecked();

      fireEvent.click(firstCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith(null);
    });

    it('handles multiple checkbox selections correctly', async () => {
      const mockOnChange = jest.fn();
      render(<DropdownTypeahead {...checkboxProps} onChange={mockOnChange} />);

      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByText('Option 1')).toBeInTheDocument();
      });

      // Select first checkbox
      const firstCheckbox = screen.getAllByRole('checkbox')[0];
      fireEvent.click(firstCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith([{value: '1', label: 'Option 1'}]);

      // Reset mock to check second call
      mockOnChange.mockClear();

      // Select second checkbox
      const secondCheckbox = screen.getAllByRole('checkbox')[1];
      fireEvent.click(secondCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith([
        {value: '1', label: 'Option 1'},
        {value: '2', label: 'Option 2'}
      ]);
    });

    it('shows selected checkboxes as checked', async () => {
      render(
        <DropdownTypeahead
          {...checkboxProps}
          selected={[{value: '1', label: 'Option 1'}, {value: '3', label: 'Option 3'}]}
        />
      );

      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByText('Option 1')).toBeInTheDocument();
      });

      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes[0]).toBeChecked(); // Option 1
      expect(checkboxes[1]).not.toBeChecked(); // Option 2
      expect(checkboxes[2]).toBeChecked(); // Option 3
    });

    it('does not render checkboxes when useCheckboxes is false', async () => {
      render(<DropdownTypeahead {...defaultProps} multiple={true} useCheckboxes={false} />);

      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByText('Option 1')).toBeInTheDocument();
      });

      // Should not have checkboxes
      const checkboxes = screen.queryAllByRole('checkbox');
      expect(checkboxes).toHaveLength(0);
    });

    it('does not render checkboxes when multiple is false', async () => {
      render(<DropdownTypeahead {...defaultProps} multiple={false} useCheckboxes={true} />);

      const input = screen.getByRole('combobox');
      fireEvent.focus(input);
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByText('Option 1')).toBeInTheDocument();
      });

      // Should not have checkboxes
      const checkboxes = screen.queryAllByRole('checkbox');
      expect(checkboxes).toHaveLength(0);
    });
  });
});
